package dto

import (
	"github.com/google/uuid"
)

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	DepartmentID uuid.UUID `json:"department_id" validate:"required,uuid"`
	UserName     string    `json:"user_name" validate:"required,max=100"`
	EmployeeID   *string   `json:"employee_id,omitempty" validate:"omitempty,max=50"`
	Email        *string   `json:"email,omitempty" validate:"omitempty,email"`
	PhoneNumber  *string   `json:"phone_number,omitempty" validate:"omitempty,max=20"`
	Password     string    `json:"password" validate:"required,min=6,max=50"`
	JobTitle     *string   `json:"job_title,omitempty" validate:"omitempty,max=100"`
	IsActive     bool      `json:"is_active"`
	RoleIDs      []uuid.UUID `json:"role_ids,omitempty" validate:"omitempty,dive,uuid"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	DepartmentID *uuid.UUID `json:"department_id,omitempty" validate:"omitempty,uuid"`
	UserName     *string    `json:"user_name,omitempty" validate:"omitempty,max=100"`
	EmployeeID   *string    `json:"employee_id,omitempty" validate:"omitempty,max=50"`
	Email        *string    `json:"email,omitempty" validate:"omitempty,email"`
	PhoneNumber  *string    `json:"phone_number,omitempty" validate:"omitempty,max=20"`
	JobTitle     *string    `json:"job_title,omitempty" validate:"omitempty,max=100"`
	IsActive     *bool      `json:"is_active,omitempty"`
	RoleIDs      []uuid.UUID `json:"role_ids,omitempty" validate:"omitempty,dive,uuid"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6,max=50"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID           uuid.UUID              `json:"id"`
	DepartmentID uuid.UUID              `json:"department_id"`
	UserName     string                 `json:"user_name"`
	EmployeeID   *string                `json:"employee_id,omitempty"`
	Email        *string                `json:"email,omitempty"`
	PhoneNumber  *string                `json:"phone_number,omitempty"`
	JobTitle     *string                `json:"job_title,omitempty"`
	IsActive     bool                   `json:"is_active"`
	CreatedAt    string                 `json:"created_at"`
	UpdatedAt    string                 `json:"updated_at"`
	Department   *DepartmentSimpleResponse `json:"department,omitempty"`
	Roles        []RoleSimpleResponse   `json:"roles,omitempty"`
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page         int        `form:"page" validate:"min=1"`
	PageSize     int        `form:"page_size" validate:"min=1,max=100"`
	Keyword      string     `form:"keyword"`
	DepartmentID *uuid.UUID `form:"department_id" validate:"omitempty,uuid"`
	IsActive     *bool      `form:"is_active"`
	RoleID       *uuid.UUID `form:"role_id" validate:"omitempty,uuid"`
}

// ImportUserRequest 导入用户请求
type ImportUserRequest struct {
	Users []CreateUserRequest `json:"users" validate:"required,dive"`
}

// ExportUserRequest 导出用户请求
type ExportUserRequest struct {
	DepartmentID *uuid.UUID `form:"department_id" validate:"omitempty,uuid"`
	IsActive     *bool      `form:"is_active"`
	Format       string     `form:"format" validate:"required,oneof=excel csv"`
}

// DepartmentSimpleResponse 部门简单响应（用于关联显示）
type DepartmentSimpleResponse struct {
	ID       uuid.UUID `json:"id"`
	DeptName string    `json:"dept_name"`
	DeptCode *string   `json:"dept_code,omitempty"`
}

// RoleSimpleResponse 角色简单响应（用于关联显示）
type RoleSimpleResponse struct {
	ID       uuid.UUID `json:"id"`
	RoleName string    `json:"role_name"`
	RoleCode *string   `json:"role_code,omitempty"`
}
