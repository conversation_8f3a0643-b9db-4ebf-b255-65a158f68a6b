package dto

import (
	"github.com/google/uuid"
)

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required,max=100"`
	Password string `json:"password" validate:"required,min=6,max=50"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	TokenType    string      `json:"token_type"`
	ExpiresIn    int64       `json:"expires_in"`
	User         UserProfile `json:"user"`
}

// RefreshTokenRequest 刷新Token请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// RefreshTokenResponse 刷新Token响应
type RefreshTokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int64  `json:"expires_in"`
}

// UserProfile 用户档案
type UserProfile struct {
	ID           uuid.UUID                 `json:"id"`
	UserName     string                    `json:"user_name"`
	EmployeeID   *string                   `json:"employee_id,omitempty"`
	Email        *string                   `json:"email,omitempty"`
	PhoneNumber  *string                   `json:"phone_number,omitempty"`
	JobTitle     *string                   `json:"job_title,omitempty"`
	IsActive     bool                      `json:"is_active"`
	Department   *DepartmentSimpleResponse `json:"department,omitempty"`
	Roles        []RoleSimpleResponse      `json:"roles,omitempty"`
	Permissions  []string                  `json:"permissions,omitempty"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6,max=50"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// ConfirmResetPasswordRequest 确认重置密码请求
type ConfirmResetPasswordRequest struct {
	Token       string `json:"token" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6,max=50"`
}

// JWTClaims JWT声明
type JWTClaims struct {
	UserID      uuid.UUID `json:"user_id"`
	UserName    string    `json:"user_name"`
	DepartmentID uuid.UUID `json:"department_id"`
	Roles       []string  `json:"roles"`
	Permissions []string  `json:"permissions"`
	TokenType   string    `json:"token_type"` // access 或 refresh
}

// TokenPair Token对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}
