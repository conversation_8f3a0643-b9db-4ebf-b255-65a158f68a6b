package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type RoleController struct {
	roleService *services.RoleService
	validator   *validator.Validate
}

func NewRoleController(roleService *services.RoleService) *RoleController {
	return &RoleController{
		roleService: roleService,
		validator:   validator.New(),
	}
}

// GetRoles 获取角色列表
// @Summary 获取角色列表
// @Description 获取角色列表，支持分页、搜索和筛选
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param is_active query bool false "是否激活"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /roles [get]
func (c *RoleController) GetRoles(ctx *gin.Context) {
	var req dto.RoleListRequest
	
	// 设置默认值
	req.Page = 1
	req.PageSize = 10
	
	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	roles, total, err := c.roleService.GetRoles(&req)
	if err != nil {
		logger.Error("Failed to get roles:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取角色列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      roles,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// CreateRole 创建角色
// @Summary 创建角色
// @Description 创建新的角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param request body dto.CreateRoleRequest true "创建角色请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /roles [post]
func (c *RoleController) CreateRole(ctx *gin.Context) {
	var req dto.CreateRoleRequest
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	role, err := c.roleService.CreateRole(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create role:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建角色失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    role,
	})
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新角色信息
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path string true "角色ID"
// @Param request body dto.UpdateRoleRequest true "更新角色请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "角色不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /roles/{id} [put]
func (c *RoleController) UpdateRole(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的角色ID",
		})
		return
	}

	var req dto.UpdateRoleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	role, err := c.roleService.UpdateRole(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update role:", err)
		if err.Error() == "角色不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新角色失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    role,
	})
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除角色（软删除）
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path string true "角色ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "角色不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /roles/{id} [delete]
func (c *RoleController) DeleteRole(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的角色ID",
		})
		return
	}

	// 调用服务
	if err := c.roleService.DeleteRole(id); err != nil {
		logger.Error("Failed to delete role:", err)
		if err.Error() == "角色不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "删除角色失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// AssignPermissions 分配权限
// @Summary 分配权限
// @Description 为角色分配权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path string true "角色ID"
// @Param request body dto.AssignPermissionsRequest true "分配权限请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "角色不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /roles/{id}/permissions [post]
func (c *RoleController) AssignPermissions(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的角色ID",
		})
		return
	}

	var req dto.AssignPermissionsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.roleService.AssignPermissions(id, &req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to assign permissions:", err)
		if err.Error() == "角色不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "分配权限失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "权限分配成功",
	})
}

// GetAvailablePermissions 获取可用权限列表
// @Summary 获取可用权限列表
// @Description 获取系统中所有可用的权限列表
// @Tags 角色管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /roles/permissions [get]
func (c *RoleController) GetAvailablePermissions(ctx *gin.Context) {
	permissions := c.roleService.GetAvailablePermissions()

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    permissions,
	})
}
