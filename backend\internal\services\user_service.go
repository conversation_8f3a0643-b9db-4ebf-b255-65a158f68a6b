package services

import (
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService(db *gorm.DB) *UserService {
	return &UserService{db: db}
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(req *dto.UserListRequest) ([]dto.UserResponse, int64, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{}).
		Preload("Department").
		Preload("Roles")

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("user_name ILIKE ? OR employee_id ILIKE ? OR email ILIKE ?", 
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.DepartmentID != nil {
		query = query.Where("department_id = ?", *req.DepartmentID)
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}
	if req.RoleID != nil {
		query = query.Joins("JOIN tbl_user_roles ON tbl_users.id = tbl_user_roles.user_id").
			Where("tbl_user_roles.role_id = ?", *req.RoleID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count users:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Find(&users).Error; err != nil {
		logger.Error("Failed to get users:", err)
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.UserResponse, len(users))
	for i, user := range users {
		responses[i] = s.convertToResponse(user)
	}

	return responses, total, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uuid.UUID) (*dto.UserResponse, error) {
	var user models.User
	if err := s.db.Preload("Department").Preload("Roles").First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		logger.Error("Failed to get user by ID:", err)
		return nil, err
	}

	response := s.convertToResponse(user)
	return &response, nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(req *dto.CreateUserRequest, creatorID uuid.UUID) (*dto.UserResponse, error) {
	// 检查部门是否存在
	var department models.Department
	if err := s.db.First(&department, req.DepartmentID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("部门不存在")
		}
		return nil, err
	}

	// 检查员工编号是否重复
	if req.EmployeeID != nil {
		var count int64
		if err := s.db.Model(&models.User{}).Where("employee_id = ?", *req.EmployeeID).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("员工编号已存在")
		}
	}

	// 检查邮箱是否重复
	if req.Email != nil {
		var count int64
		if err := s.db.Model(&models.User{}).Where("email = ?", *req.Email).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("邮箱已存在")
		}
	}

	// 检查手机号是否重复
	if req.PhoneNumber != nil {
		var count int64
		if err := s.db.Model(&models.User{}).Where("phone_number = ?", *req.PhoneNumber).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("手机号已存在")
		}
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("Failed to hash password:", err)
		return nil, errors.New("密码加密失败")
	}

	// 创建用户
	user := models.User{
		DepartmentID: &req.DepartmentID,
		UserName:     req.UserName,
		EmployeeID:   req.EmployeeID,
		Email:        req.Email,
		PhoneNumber:  req.PhoneNumber,
		PasswordHash: string(hashedPassword),
		JobTitle:     req.JobTitle,
		IsActive:     req.IsActive,
	}
	user.CreatedBy = &creatorID

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建用户
	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to create user:", err)
		return nil, err
	}

	// 分配角色
	if len(req.RoleIDs) > 0 {
		var roles []models.Role
		if err := tx.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
		if len(roles) != len(req.RoleIDs) {
			tx.Rollback()
			return nil, errors.New("部分角色不存在")
		}
		if err := tx.Model(&user).Association("Roles").Append(roles); err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Department").Preload("Roles").First(&user, user.ID).Error; err != nil {
		logger.Error("Failed to preload user data:", err)
		return nil, err
	}

	response := s.convertToResponse(user)
	return &response, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(id uuid.UUID, req *dto.UpdateUserRequest, updaterID uuid.UUID) (*dto.UserResponse, error) {
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	// 检查部门是否存在
	if req.DepartmentID != nil {
		var department models.Department
		if err := s.db.First(&department, *req.DepartmentID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("部门不存在")
			}
			return nil, err
		}
	}

	// 检查员工编号是否重复
	if req.EmployeeID != nil {
		var count int64
		if err := s.db.Model(&models.User{}).Where("employee_id = ? AND id != ?", *req.EmployeeID, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("员工编号已存在")
		}
	}

	// 检查邮箱是否重复
	if req.Email != nil {
		var count int64
		if err := s.db.Model(&models.User{}).Where("email = ? AND id != ?", *req.Email, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("邮箱已存在")
		}
	}

	// 检查手机号是否重复
	if req.PhoneNumber != nil {
		var count int64
		if err := s.db.Model(&models.User{}).Where("phone_number = ? AND id != ?", *req.PhoneNumber, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("手机号已存在")
		}
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新字段
	updates := make(map[string]interface{})
	if req.DepartmentID != nil {
		updates["department_id"] = *req.DepartmentID
	}
	if req.UserName != nil {
		updates["user_name"] = *req.UserName
	}
	if req.EmployeeID != nil {
		updates["employee_id"] = *req.EmployeeID
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.PhoneNumber != nil {
		updates["phone_number"] = *req.PhoneNumber
	}
	if req.JobTitle != nil {
		updates["job_title"] = *req.JobTitle
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	updates["updated_by"] = updaterID

	if err := tx.Model(&user).Updates(updates).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to update user:", err)
		return nil, err
	}

	// 更新角色
	if req.RoleIDs != nil {
		// 清除现有角色
		if err := tx.Model(&user).Association("Roles").Clear(); err != nil {
			tx.Rollback()
			return nil, err
		}
		
		// 分配新角色
		if len(req.RoleIDs) > 0 {
			var roles []models.Role
			if err := tx.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
			if len(roles) != len(req.RoleIDs) {
				tx.Rollback()
				return nil, errors.New("部分角色不存在")
			}
			if err := tx.Model(&user).Association("Roles").Append(roles); err != nil {
				tx.Rollback()
				return nil, err
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Department").Preload("Roles").First(&user, id).Error; err != nil {
		logger.Error("Failed to preload user data:", err)
		return nil, err
	}

	response := s.convertToResponse(user)
	return &response, nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(id uuid.UUID) error {
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return err
	}

	// 软删除用户
	if err := s.db.Delete(&user).Error; err != nil {
		logger.Error("Failed to delete user:", err)
		return err
	}

	return nil
}

// convertToResponse 转换为响应格式
func (s *UserService) convertToResponse(user models.User) dto.UserResponse {
	response := dto.UserResponse{
		ID:          user.ID,
		DepartmentID: *user.DepartmentID,
		UserName:    user.UserName,
		EmployeeID:  user.EmployeeID,
		Email:       user.Email,
		PhoneNumber: user.PhoneNumber,
		JobTitle:    user.JobTitle,
		IsActive:    user.IsActive,
		CreatedAt:   user.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   user.UpdatedAt.Format(time.RFC3339),
	}

	if user.Department != nil {
		response.Department = &dto.DepartmentSimpleResponse{
			ID:       user.Department.ID,
			DeptName: user.Department.DeptName,
			DeptCode: user.Department.DeptCode,
		}
	}

	if len(user.Roles) > 0 {
		response.Roles = make([]dto.RoleSimpleResponse, len(user.Roles))
		for i, role := range user.Roles {
			response.Roles[i] = dto.RoleSimpleResponse{
				ID:       role.ID,
				RoleName: role.RoleName,
				RoleCode: role.RoleCode,
			}
		}
	}

	return response
}
