package router

import (
	"hospital-management/internal/config"
	"hospital-management/internal/controllers"
	"hospital-management/internal/middleware"
	"hospital-management/internal/services"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

func Setup(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())

	// Swagger文档
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "Hospital Management System API is running",
		})
	})

	// 初始化服务
	departmentService := services.NewDepartmentService(db)
	userService := services.NewUserService(db)
	roleService := services.NewRoleService(db)
	authService := services.NewAuthService(db, cfg.JWTSecret)

	// 初始化控制器
	departmentController := controllers.NewDepartmentController(departmentService)
	userController := controllers.NewUserController(userService)
	roleController := controllers.NewRoleController(roleService)
	authController := controllers.NewAuthController(authService)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/login", authController.Login)
			auth.POST("/refresh", authController.RefreshToken)
		}

		// 需要认证的认证相关接口
		authProtected := api.Group("/auth")
		authProtected.Use(middleware.JWTAuth(cfg.JWTSecret))
		{
			authProtected.POST("/logout", authController.Logout)
			authProtected.GET("/profile", authController.GetProfile)
			authProtected.POST("/change-password", authController.ChangePassword)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.JWTAuth(cfg.JWTSecret))
		{
			// 用户管理
			users := protected.Group("/users")
			{
				users.GET("", userController.GetUsers)
				users.POST("", userController.CreateUser)
				users.GET("/:id", userController.GetUserByID)
				users.PUT("/:id", userController.UpdateUser)
				users.DELETE("/:id", userController.DeleteUser)
				users.POST("/import", userController.ImportUsers)
				users.GET("/export", userController.ExportUsers)
			}

			// 部门管理
			departments := protected.Group("/departments")
			{
				departments.GET("", departmentController.GetDepartments)
				departments.POST("", departmentController.CreateDepartment)
				departments.GET("/tree", departmentController.GetDepartmentTree)
				departments.PUT("/:id", departmentController.UpdateDepartment)
				departments.DELETE("/:id", departmentController.DeleteDepartment)
			}

			// 角色管理
			roles := protected.Group("/roles")
			{
				roles.GET("", roleController.GetRoles)
				roles.POST("", roleController.CreateRole)
				roles.PUT("/:id", roleController.UpdateRole)
				roles.DELETE("/:id", roleController.DeleteRole)
				roles.POST("/:id/permissions", roleController.AssignPermissions)
				roles.GET("/permissions", roleController.GetAvailablePermissions)
			}

			// 预算管理
			budget := protected.Group("/budget")
			{
				budget.GET("/schemes", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get budget schemes - TODO"})
				})
				budget.POST("/schemes", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create budget scheme - TODO"})
				})
			}

			// 报销管理
			expense := protected.Group("/expense")
			{
				expense.GET("/applications", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get expense applications - TODO"})
				})
				expense.POST("/applications", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create expense application - TODO"})
				})
			}

			// 审批管理
			approval := protected.Group("/approval")
			{
				approval.GET("/todo", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get todo approvals - TODO"})
				})
				approval.POST("/approve/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Approve - TODO"})
				})
			}
		}
	}

	return r
}
