# 医院内部控制与运营管理系统 - 后端功能点

## 模块一：统一支撑平台 (Foundation Platform)

### 1.1 组织权限管理
- **部门管理API** √
  - `GET /api/v1/departments` - 获取部门列表（支持树形结构） √
  - `POST /api/v1/departments` - 创建部门 √
  - `PUT /api/v1/departments/:id` - 更新部门信息 √
  - `DELETE /api/v1/departments/:id` - 删除部门 √
  - `GET /api/v1/departments/tree` - 获取部门树形结构 √

- **用户管理API** √
  - `GET /api/v1/users` - 获取用户列表（分页、搜索、筛选） √
  - `POST /api/v1/users` - 创建用户 √
  - `GET /api/v1/users/:id` - 获取用户详情 √
  - `PUT /api/v1/users/:id` - 更新用户信息 √
  - `DELETE /api/v1/users/:id` - 删除用户 √
  - `POST /api/v1/users/import` - 批量导入用户 √
  - `GET /api/v1/users/export` - 导出用户数据 √

- **角色管理API** √
  - `GET /api/v1/roles` - 获取角色列表 √
  - `POST /api/v1/roles` - 创建角色 √
  - `PUT /api/v1/roles/:id` - 更新角色 √
  - `DELETE /api/v1/roles/:id` - 删除角色 √
  - `POST /api/v1/roles/:id/permissions` - 分配权限 √

- **认证授权API** √
  - `POST /api/v1/auth/login` - 用户登录 √
  - `POST /api/v1/auth/logout` - 用户登出 √
  - `POST /api/v1/auth/refresh` - 刷新Token √
  - `GET /api/v1/auth/profile` - 获取当前用户信息 √

### 1.2 工作流引擎
- **审批流API**
  - `POST /api/v1/approval/flows` - 创建审批流实例
  - `GET /api/v1/approval/flows/:id` - 获取审批流详情
  - `PUT /api/v1/approval/flows/:id/status` - 更新审批流状态
  - `GET /api/v1/approval/todo` - 获取待办任务
  - `POST /api/v1/approval/approve/:nodeId` - 审批操作
  - `POST /api/v1/approval/reject/:nodeId` - 驳回操作
  - `POST /api/v1/approval/transfer/:nodeId` - 转办操作

### 1.3 消息中心
- **消息API**
  - `GET /api/v1/messages` - 获取消息列表
  - `POST /api/v1/messages` - 发送消息
  - `PUT /api/v1/messages/:id/read` - 标记已读
  - `GET /api/v1/messages/unread-count` - 获取未读消息数量

### 1.4 文件管理
- **文件API**
  - `POST /api/v1/files/upload` - 文件上传
  - `GET /api/v1/files/:id/download` - 文件下载
  - `DELETE /api/v1/files/:id` - 删除文件
  - `GET /api/v1/files/business/:businessId` - 获取业务关联文件

## 模块二：全面预算管理

### 2.1 预算编制
- **预算方案API**
  - `GET /api/v1/budget/schemes` - 获取预算方案列表
  - `POST /api/v1/budget/schemes` - 创建预算方案
  - `PUT /api/v1/budget/schemes/:id` - 更新预算方案
  - `DELETE /api/v1/budget/schemes/:id` - 删除预算方案

- **预算科目API**
  - `GET /api/v1/budget/subjects` - 获取预算科目列表
  - `POST /api/v1/budget/subjects` - 创建预算科目
  - `PUT /api/v1/budget/subjects/:id` - 更新预算科目
  - `GET /api/v1/budget/subjects/tree` - 获取科目树形结构

- **预算明细API**
  - `GET /api/v1/budget/items` - 获取预算明细
  - `POST /api/v1/budget/items` - 创建预算明细
  - `PUT /api/v1/budget/items/:id` - 更新预算明细
  - `POST /api/v1/budget/items/batch` - 批量创建预算明细

### 2.2 预算控制
- **预算控制API**
  - `POST /api/v1/budget/freeze` - 冻结预算额度
  - `POST /api/v1/budget/unfreeze` - 解冻预算额度
  - `POST /api/v1/budget/consume` - 消费预算额度
  - `GET /api/v1/budget/balance/:itemId` - 获取预算余额

### 2.3 预算分析
- **预算分析API**
  - `GET /api/v1/budget/analysis/execution` - 预算执行分析
  - `GET /api/v1/budget/analysis/department` - 部门预算分析
  - `GET /api/v1/budget/analysis/subject` - 科目预算分析

## 模块三：支出控制管理

### 3.1 事前申请
- **事前申请API**
  - `GET /api/v1/pre-applications` - 获取事前申请列表
  - `POST /api/v1/pre-applications` - 创建事前申请
  - `GET /api/v1/pre-applications/:id` - 获取申请详情
  - `PUT /api/v1/pre-applications/:id` - 更新申请
  - `POST /api/v1/pre-applications/:id/submit` - 提交申请

### 3.2 费用报销
- **报销申请API**
  - `GET /api/v1/expense/applications` - 获取报销申请列表
  - `POST /api/v1/expense/applications` - 创建报销申请
  - `GET /api/v1/expense/applications/:id` - 获取报销详情
  - `PUT /api/v1/expense/applications/:id` - 更新报销申请
  - `POST /api/v1/expense/applications/:id/submit` - 提交报销申请
  - `GET /api/v1/expense/applications/my` - 获取我的报销记录

### 3.3 付款管理
- **付款API**
  - `GET /api/v1/payments` - 获取付款单列表
  - `POST /api/v1/payments` - 创建付款单
  - `PUT /api/v1/payments/:id/status` - 更新付款状态
  - `POST /api/v1/payments/batch` - 批量付款

## 模块四：采购管理

### 4.1 供应商管理
- **供应商API**
  - `GET /api/v1/suppliers` - 获取供应商列表
  - `POST /api/v1/suppliers` - 创建供应商
  - `PUT /api/v1/suppliers/:id` - 更新供应商信息
  - `PUT /api/v1/suppliers/:id/status` - 更新供应商状态

### 4.2 采购申请
- **采购申请API**
  - `GET /api/v1/purchase/requisitions` - 获取采购申请列表
  - `POST /api/v1/purchase/requisitions` - 创建采购申请
  - `PUT /api/v1/purchase/requisitions/:id` - 更新采购申请
  - `POST /api/v1/purchase/requisitions/:id/submit` - 提交采购申请

## 模块五：合同管理

### 5.1 合同管理
- **合同API**
  - `GET /api/v1/contracts` - 获取合同列表
  - `POST /api/v1/contracts` - 创建合同
  - `GET /api/v1/contracts/:id` - 获取合同详情
  - `PUT /api/v1/contracts/:id` - 更新合同
  - `PUT /api/v1/contracts/:id/status` - 更新合同状态

- **付款计划API**
  - `GET /api/v1/contracts/:id/payment-schedules` - 获取付款计划
  - `POST /api/v1/contracts/:id/payment-schedules` - 创建付款计划
  - `PUT /api/v1/payment-schedules/:id` - 更新付款计划

## 模块六：资产管理

### 6.1 资产管理
- **资产分类API**
  - `GET /api/v1/asset/categories` - 获取资产分类
  - `POST /api/v1/asset/categories` - 创建资产分类
  - `PUT /api/v1/asset/categories/:id` - 更新资产分类

- **资产API**
  - `GET /api/v1/assets` - 获取资产列表
  - `POST /api/v1/assets` - 创建资产
  - `GET /api/v1/assets/:id` - 获取资产详情
  - `PUT /api/v1/assets/:id` - 更新资产信息
  - `POST /api/v1/assets/:id/transfer` - 资产转移
  - `POST /api/v1/assets/:id/dispose` - 资产处置

## 通用功能
- **数据字典API**
  - `GET /api/v1/dict/:type` - 获取字典数据
- **统计报表API**
  - `GET /api/v1/reports/dashboard` - 仪表板数据
  - `GET /api/v1/reports/export` - 导出报表
