package jwt

import (
	"errors"
	"hospital-management/internal/dto"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

const (
	AccessTokenExpiry  = 24 * time.Hour    // 访问令牌过期时间：24小时
	RefreshTokenExpiry = 7 * 24 * time.Hour // 刷新令牌过期时间：7天
)

type JWTManager struct {
	secretKey string
}

func NewJWTManager(secretKey string) *JWTManager {
	return &JWTManager{
		secretKey: secretKey,
	}
}

// GenerateTokenPair 生成访问令牌和刷新令牌对
func (j *JWTManager) GenerateTokenPair(userID uuid.UUID, userName string, departmentID uuid.UUID, roles []string, permissions []string) (*dto.TokenPair, error) {
	// 生成访问令牌
	accessToken, err := j.generateToken(userID, userName, departmentID, roles, permissions, "access", AccessTokenExpiry)
	if err != nil {
		return nil, err
	}

	// 生成刷新令牌
	refreshToken, err := j.generateToken(userID, userName, departmentID, roles, permissions, "refresh", RefreshTokenExpiry)
	if err != nil {
		return nil, err
	}

	return &dto.TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(AccessTokenExpiry.Seconds()),
	}, nil
}

// generateToken 生成JWT令牌
func (j *JWTManager) generateToken(userID uuid.UUID, userName string, departmentID uuid.UUID, roles []string, permissions []string, tokenType string, expiry time.Duration) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"user_id":       userID.String(),
		"user_name":     userName,
		"department_id": departmentID.String(),
		"roles":         roles,
		"permissions":   permissions,
		"token_type":    tokenType,
		"iat":           now.Unix(),
		"exp":           now.Add(expiry).Unix(),
		"nbf":           now.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secretKey))
}

// ValidateToken 验证JWT令牌
func (j *JWTManager) ValidateToken(tokenString string) (*dto.JWTClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return []byte(j.secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	// 解析用户ID
	userIDStr, ok := claims["user_id"].(string)
	if !ok {
		return nil, errors.New("invalid user_id in token")
	}
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, errors.New("invalid user_id format")
	}

	// 解析部门ID
	departmentIDStr, ok := claims["department_id"].(string)
	if !ok {
		return nil, errors.New("invalid department_id in token")
	}
	departmentID, err := uuid.Parse(departmentIDStr)
	if err != nil {
		return nil, errors.New("invalid department_id format")
	}

	// 解析用户名
	userName, ok := claims["user_name"].(string)
	if !ok {
		return nil, errors.New("invalid user_name in token")
	}

	// 解析令牌类型
	tokenType, ok := claims["token_type"].(string)
	if !ok {
		return nil, errors.New("invalid token_type in token")
	}

	// 解析角色列表
	var roles []string
	if rolesInterface, ok := claims["roles"].([]interface{}); ok {
		for _, role := range rolesInterface {
			if roleStr, ok := role.(string); ok {
				roles = append(roles, roleStr)
			}
		}
	}

	// 解析权限列表
	var permissions []string
	if permissionsInterface, ok := claims["permissions"].([]interface{}); ok {
		for _, permission := range permissionsInterface {
			if permissionStr, ok := permission.(string); ok {
				permissions = append(permissions, permissionStr)
			}
		}
	}

	return &dto.JWTClaims{
		UserID:      userID,
		UserName:    userName,
		DepartmentID: departmentID,
		Roles:       roles,
		Permissions: permissions,
		TokenType:   tokenType,
	}, nil
}

// RefreshAccessToken 刷新访问令牌
func (j *JWTManager) RefreshAccessToken(refreshToken string) (string, error) {
	claims, err := j.ValidateToken(refreshToken)
	if err != nil {
		return "", err
	}

	if claims.TokenType != "refresh" {
		return "", errors.New("invalid token type for refresh")
	}

	// 生成新的访问令牌
	accessToken, err := j.generateToken(
		claims.UserID,
		claims.UserName,
		claims.DepartmentID,
		claims.Roles,
		claims.Permissions,
		"access",
		AccessTokenExpiry,
	)
	if err != nil {
		return "", err
	}

	return accessToken, nil
}

// ExtractTokenFromHeader 从Authorization头中提取令牌
func ExtractTokenFromHeader(authHeader string) string {
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		return authHeader[7:]
	}
	return ""
}
