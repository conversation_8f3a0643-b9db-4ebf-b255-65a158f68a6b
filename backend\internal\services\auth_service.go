package services

import (
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/jwt"
	"hospital-management/pkg/logger"
	"strings"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AuthService struct {
	db         *gorm.DB
	jwtManager *jwt.JWTManager
}

func NewAuthService(db *gorm.DB, jwtSecret string) *AuthService {
	return &AuthService{
		db:         db,
		jwtManager: jwt.NewJWTManager(jwtSecret),
	}
}

// Login 用户登录
func (s *AuthService) Login(req *dto.LoginRequest) (*dto.LoginResponse, error) {
	// 查找用户（支持用户名、员工编号、邮箱、手机号登录）
	var user models.User
	query := s.db.Preload("Department").Preload("Roles").Where("is_active = ?", true)
	
	// 尝试不同的登录方式
	err := query.Where("user_name = ? OR employee_id = ? OR email = ? OR phone_number = ?", 
		req.Username, req.Username, req.Username, req.Username).First(&user).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户名或密码错误")
		}
		logger.Error("Failed to find user:", err)
		return nil, errors.New("登录失败")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 获取用户角色和权限
	roles := make([]string, len(user.Roles))
	var allPermissions []string
	
	for i, role := range user.Roles {
		roles[i] = role.RoleName
		if role.Permissions != "" {
			permissions := strings.Split(role.Permissions, ",")
			allPermissions = append(allPermissions, permissions...)
		}
	}

	// 去重权限
	permissionMap := make(map[string]bool)
	var uniquePermissions []string
	for _, permission := range allPermissions {
		if !permissionMap[permission] {
			permissionMap[permission] = true
			uniquePermissions = append(uniquePermissions, permission)
		}
	}

	// 生成JWT令牌对
	tokenPair, err := s.jwtManager.GenerateTokenPair(
		user.ID,
		user.UserName,
		*user.DepartmentID,
		roles,
		uniquePermissions,
	)
	if err != nil {
		logger.Error("Failed to generate token pair:", err)
		return nil, errors.New("生成令牌失败")
	}

	// 构建用户档案
	userProfile := dto.UserProfile{
		ID:          user.ID,
		UserName:    user.UserName,
		EmployeeID:  user.EmployeeID,
		Email:       user.Email,
		PhoneNumber: user.PhoneNumber,
		JobTitle:    user.JobTitle,
		IsActive:    user.IsActive,
		Permissions: uniquePermissions,
	}

	if user.Department != nil {
		userProfile.Department = &dto.DepartmentSimpleResponse{
			ID:       user.Department.ID,
			DeptName: user.Department.DeptName,
			DeptCode: user.Department.DeptCode,
		}
	}

	if len(user.Roles) > 0 {
		userProfile.Roles = make([]dto.RoleSimpleResponse, len(user.Roles))
		for i, role := range user.Roles {
			userProfile.Roles[i] = dto.RoleSimpleResponse{
				ID:       role.ID,
				RoleName: role.RoleName,
				RoleCode: role.RoleCode,
			}
		}
	}

	return &dto.LoginResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    tokenPair.ExpiresIn,
		User:         userProfile,
	}, nil
}

// RefreshToken 刷新访问令牌
func (s *AuthService) RefreshToken(req *dto.RefreshTokenRequest) (*dto.RefreshTokenResponse, error) {
	accessToken, err := s.jwtManager.RefreshAccessToken(req.RefreshToken)
	if err != nil {
		logger.Error("Failed to refresh token:", err)
		return nil, errors.New("刷新令牌失败")
	}

	return &dto.RefreshTokenResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int64(jwt.AccessTokenExpiry.Seconds()),
	}, nil
}

// GetProfile 获取当前用户档案
func (s *AuthService) GetProfile(userID uuid.UUID) (*dto.UserProfile, error) {
	var user models.User
	if err := s.db.Preload("Department").Preload("Roles").First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		logger.Error("Failed to get user profile:", err)
		return nil, err
	}

	// 获取用户权限
	var allPermissions []string
	for _, role := range user.Roles {
		if role.Permissions != "" {
			permissions := strings.Split(role.Permissions, ",")
			allPermissions = append(allPermissions, permissions...)
		}
	}

	// 去重权限
	permissionMap := make(map[string]bool)
	var uniquePermissions []string
	for _, permission := range allPermissions {
		if !permissionMap[permission] {
			permissionMap[permission] = true
			uniquePermissions = append(uniquePermissions, permission)
		}
	}

	// 构建用户档案
	userProfile := dto.UserProfile{
		ID:          user.ID,
		UserName:    user.UserName,
		EmployeeID:  user.EmployeeID,
		Email:       user.Email,
		PhoneNumber: user.PhoneNumber,
		JobTitle:    user.JobTitle,
		IsActive:    user.IsActive,
		Permissions: uniquePermissions,
	}

	if user.Department != nil {
		userProfile.Department = &dto.DepartmentSimpleResponse{
			ID:       user.Department.ID,
			DeptName: user.Department.DeptName,
			DeptCode: user.Department.DeptCode,
		}
	}

	if len(user.Roles) > 0 {
		userProfile.Roles = make([]dto.RoleSimpleResponse, len(user.Roles))
		for i, role := range user.Roles {
			userProfile.Roles[i] = dto.RoleSimpleResponse{
				ID:       role.ID,
				RoleName: role.RoleName,
				RoleCode: role.RoleCode,
			}
		}
	}

	return &userProfile, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID uuid.UUID, req *dto.ChangePasswordRequest) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.OldPassword)); err != nil {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("Failed to hash password:", err)
		return errors.New("密码加密失败")
	}

	// 更新密码
	if err := s.db.Model(&user).Update("password_hash", string(hashedPassword)).Error; err != nil {
		logger.Error("Failed to update password:", err)
		return errors.New("密码更新失败")
	}

	return nil
}

// ValidateToken 验证令牌
func (s *AuthService) ValidateToken(tokenString string) (*dto.JWTClaims, error) {
	return s.jwtManager.ValidateToken(tokenString)
}
