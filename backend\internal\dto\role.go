package dto

import (
	"github.com/google/uuid"
)

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	RoleName    string   `json:"role_name" validate:"required,max=100"`
	RoleCode    *string  `json:"role_code,omitempty" validate:"omitempty,max=50"`
	Description *string  `json:"description,omitempty" validate:"omitempty,max=500"`
	IsActive    bool     `json:"is_active"`
	Permissions []string `json:"permissions,omitempty" validate:"omitempty,dive,max=100"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	RoleName    *string  `json:"role_name,omitempty" validate:"omitempty,max=100"`
	RoleCode    *string  `json:"role_code,omitempty" validate:"omitempty,max=50"`
	Description *string  `json:"description,omitempty" validate:"omitempty,max=500"`
	IsActive    *bool    `json:"is_active,omitempty"`
	Permissions []string `json:"permissions,omitempty" validate:"omitempty,dive,max=100"`
}

// RoleResponse 角色响应
type RoleResponse struct {
	ID          uuid.UUID `json:"id"`
	RoleName    string    `json:"role_name"`
	RoleCode    *string   `json:"role_code,omitempty"`
	Description *string   `json:"description,omitempty"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   string    `json:"created_at"`
	UpdatedAt   string    `json:"updated_at"`
	Permissions []string  `json:"permissions,omitempty"`
	UserCount   int64     `json:"user_count,omitempty"`
}

// RoleListRequest 角色列表请求
type RoleListRequest struct {
	Page     int    `form:"page" validate:"min=1"`
	PageSize int    `form:"page_size" validate:"min=1,max=100"`
	Keyword  string `form:"keyword"`
	IsActive *bool  `form:"is_active"`
}

// AssignPermissionsRequest 分配权限请求
type AssignPermissionsRequest struct {
	Permissions []string `json:"permissions" validate:"required,dive,max=100"`
}

// PermissionResponse 权限响应
type PermissionResponse struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
}
